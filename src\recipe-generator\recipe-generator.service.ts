import { Injectable } from '@nestjs/common';
import Tik<PERSON> from '@tobyg74/tiktok-api-dl';
import z from 'zod';
import {
  AIMessage,
  BaseMessage,
  SystemMessage,
} from '@langchain/core/messages';
import { StateGraph, START, END, Annotation } from '@langchain/langgraph';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { ttdl } from 'btch-downloader';

export interface RecipeGeneratorInput {
  url: string;
  language?: string;
}

export interface VideoInfo {
  videoPath: string;
  videoDescription: string;
}

// Define schemas outside the class for type inference
const ingredientSchema = z.object({
  quantity: z.number().positive(),
  unit: z.string().min(1),
  name: z.string().min(1),
});

const stepSchema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
});

// TODO: improve metadata schema
const metadataSchema = z.object({
  audio_helpful: z.boolean().optional(),
  video_type: z.enum(['cinematic', 'vlog', 'tutorial', 'other']).optional(),
});

const recipeSchema = z.object({
  title: z.string().min(1).max(200),
  category: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
  cooking_time_minutes: z.number().int().positive().nullable().optional(),
  servings: z.number().int().positive().nullable().optional(),
  ingredients: z.array(ingredientSchema).default([]),
  steps: z.array(stepSchema).default([]),
  metadata: metadataSchema.optional(),
});

// Export the type for use elsewhere
export type RecipeOutput = z.infer<typeof recipeSchema>;

@Injectable()
export class RecipeGeneratorService {
  // System prompt for recipe generation
  private readonly systemPrompt = `
You are an expert culinary assistant.
You will receive a cooking video (e.g., TikTok) and the video description.
Extract a clean, structured recipe from both the video content and the video description, as ingredients and steps may be mentioned in either source.
If there is insufficient info, make minimal reasonable assumptions but mark unknowns as null.

You must respond with valid XML following this exact schema:

<recipe>
  <title>Recipe title (1-200 characters)</title>
  <category>Recipe category (optional)</category>
  <description>Recipe description (optional)</description>
  <cooking_time_minutes>Cooking time in minutes (number or null)</cooking_time_minutes>
  <servings>Number of servings (number or null)</servings>
  <ingredients>
    <ingredient>
      <quantity>Numeric quantity (positive number)</quantity>
      <unit>Unit of measurement (string)</unit>
      <name>Ingredient name (string)</name>
    </ingredient>
    <!-- Repeat for each ingredient -->
  </ingredients>
  <steps>
    <step>
      <name>Step name (string)</name>
      <description>Step description (string)</description>
    </step>
    <!-- Repeat for each step -->
  </steps>
  <metadata>
    <audio_helpful>true/false (optional)</audio_helpful>
    <video_type>cinematic/vlog/tutorial/other (optional)</video_type>
  </metadata>
</recipe>

Output requirements:
- Keep measurements and quantities if mentioned
- Keep the order of steps as presented
- Include time and servings only if clearly stated
- Use concise, clear language
- Return ONLY the XML, no additional text or explanations
`;

  constructor() {}

  private AgentState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
      reducer: (x, y) => x.concat(y),
      default: () => [new SystemMessage(this.systemPrompt)],
    }),
    input: Annotation<RecipeGeneratorInput>(),
    videoInfo: Annotation<VideoInfo>(),
    output: Annotation<RecipeOutput>(),
  });

  private async getContentNode(state: typeof this.AgentState.State) {
    const url = state.input.url;
    let videoPath = '';
    let videoDescription = '';

    try {
      // Primary method: Use downloadTikTokVideo
      console.log('Attempting to download video using primary method...');
      const result = await this.downloadTikTokVideoToby74(url);
      videoPath = result.videoPath;
      videoDescription = result.videoDescription || '';
      console.log('Video downloaded successfully:', videoPath);
    } catch (primaryError) {
      const primaryErrorMsg =
        primaryError instanceof Error
          ? primaryError.message
          : String(primaryError);
      console.log('Primary download method failed:', primaryErrorMsg);

      try {
        const fallbackResult = await this.downloadTiktokVideoBTCH(url);
        videoPath = fallbackResult.videoPath;
        videoDescription = fallbackResult.videoDescription;
      } catch (fallbackError) {
        const fallbackErrorMsg =
          fallbackError instanceof Error
            ? fallbackError.message
            : String(fallbackError);
        console.error('Both download methods failed:');
        console.error('Primary error:', primaryErrorMsg);
        console.error('Fallback error:', fallbackErrorMsg);
        throw new Error(`Failed to download video: ${primaryErrorMsg}`);
      }
    }

    return { videoInfo: { videoPath, videoDescription } };
  }

  private shouldContinueNode(state: typeof this.AgentState.State) {
    const lastMessage = state.messages[state.messages.length - 1];

    if (lastMessage && !(lastMessage as AIMessage).tool_calls?.length) {
      return END;
    }

    return 'action';
  }

  private async agentNode(state: typeof this.AgentState.State) {
    const messages = state.messages;
    console.log('Invoking model with messages:', messages.length);

    const gemini = new ChatGoogleGenerativeAI({
      model: 'gemini-1.5-pro',
      temperature: 0,
      maxRetries: 2,
    });

    const geminiWithSchema = gemini.withStructuredOutput(recipeSchema, {
      method: 'json_mode',
      name: 'recipe',
    });

    try {
      const response = await geminiWithSchema.invoke(messages);

      return {
        messages: [response],
        output: response,
      };
    } catch (err) {
      console.error('Model invocation error:', err);
      return { messages: [new AIMessage('error')] };
    }
  }

  // Getter methods for accessing schemas and prompts
  getRecipeSchema() {
    return recipeSchema;
  }

  getSystemPrompt(): string {
    return this.systemPrompt;
  }

  // Validate recipe data against schema
  validateRecipe(data: unknown): RecipeOutput {
    return recipeSchema.parse(data);
  }

  async downloadTikTokVideoToby74(url: string): Promise<VideoInfo> {
    if (!url) throw new Error('Missing TikTok URL');
    const outDir = path.resolve('cookthat', 'downloads');
    await fs.promises.mkdir(outDir, { recursive: true });

    // Explicitly use version v1 to get TiktokAPIResponse with Content type
    const result = await Tiktok.Downloader(url, { version: 'v1' });
    if (result?.status !== 'success' || !result?.result) {
      throw new Error('TikTok API DL did not return success');
    }

    const videoDescription = result.result.desc ?? '';

    // Type guard to ensure we have video data
    if (!result.result.video || !result.result.video.playAddr) {
      throw new Error('No video data found for this TikTok');
    }

    const playAddr = result.result.video.playAddr;
    if (!Array.isArray(playAddr) || playAddr.length === 0) {
      throw new Error('No video URL found for this TikTok');
    }
    const videoUrl = playAddr[1] || playAddr[0];

    const fileName = `tiktok_video_${Date.now()}.mp4`;
    const videoPath = path.join(outDir, fileName);

    const response = await axios.get(videoUrl, { responseType: 'stream' });
    await new Promise((resolve, reject) => {
      const writer = fs.createWriteStream(videoPath);
      response.data.pipe(writer);
      writer.on('finish', resolve);
      writer.on('error', reject);
    });

    return { videoPath, videoDescription };
  }

  async downloadTiktokVideoBTCH(url: string): Promise<VideoInfo> {
    let videoPath = '';
    let videoDescription = '';

    console.log('Attempting fallback download method...');
    const btchResult = await ttdl(url);

    if (btchResult && btchResult.video && btchResult.video.length > 0) {
      const videoUrl = btchResult.video[0];
      videoDescription = btchResult.title || '';

      // Download the video from the URL
      const outDir = path.resolve('cookthat', 'downloads');
      await fs.promises.mkdir(outDir, { recursive: true });

      const fileName = `tiktok_video_fallback_${Date.now()}.mp4`;
      videoPath = path.join(outDir, fileName);

      const response = await axios.get(videoUrl, { responseType: 'stream' });
      await new Promise<void>((resolve, reject) => {
        const writer = fs.createWriteStream(videoPath);
        response.data.pipe(writer);
        writer.on('finish', () => resolve());
        writer.on('error', reject);
      });

      console.log('Fallback video downloaded successfully:', videoPath);
      return { videoPath, videoDescription };
    } else {
      throw new Error('No video URL found in btch-downloader result');
    }
  }

  // Main recipe generation method (to be implemented)
  generateRecipe({
    url,
    language,
  }: RecipeGeneratorInput): Promise<RecipeOutput> {
    // TODO: Implement recipe generation logic
    // This would integrate with your agent system

    console.log('Generating recipe for:', url, language);
    throw new Error('Method not implemented');
  }
}
